import {
  BookOpen,
  ChevronDown,
  ChevronRight,
  Clock,
  Folder,
  Menu,
  Plus,
  Star,
  Tag,
  Zap
} from 'lucide-react'
import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'

interface SidebarProps {
  collapsed: boolean
  onCollapse: (collapsed: boolean) => void
}

export const Sidebar: React.FC<SidebarProps> = ({ collapsed, onCollapse }) => {
  const {
    bookmarks,
    collections,
    tags,
    selectedCollection,
    setSelectedCollection,
    selectedTags,
    setSelectedTags
  } = useBookmarks()

  const [collectionsExpanded, setCollectionsExpanded] = useState(true)
  const [tagsExpanded, setTagsExpanded] = useState(true)
  const [quickActionsExpanded, setQuickActionsExpanded] = useState(true)

  const totalBookmarks = bookmarks.length
  const favoriteBookmarks = bookmarks.filter(b => b.isFavorite).length
  const recentBookmarks = bookmarks.filter(b => {
    const dayAgo = new Date()
    dayAgo.setDate(dayAgo.getDate() - 1)
    return new Date(b.dateAdded) > dayAgo
  }).length

  const handleTagToggle = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag]
    setSelectedTags(newTags)
  }

  const getTagCount = (tag: string) => {
    return bookmarks.filter(bookmark => bookmark.tags.includes(tag)).length
  }

  return (
    <aside className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header">
        <button
          onClick={() => onCollapse(!collapsed)}
          className="collapse-btn"
          aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <Menu size={20} />
        </button>
        {!collapsed && <span className="sidebar-title">Bookmark Studio</span>}
      </div>

      <div className="sidebar-content">
        {/* Library Section */}
        <div className="sidebar-section">
          {!collapsed && (
            <div className="section-header">
              <h3 className="section-title">Library</h3>
              <div className="library-stats">
                <span className="stat">{totalBookmarks} total</span>
                <span className="stat">{collections.length} collections</span>
              </div>
            </div>
          )}

          <nav className="nav-list">
            <button
              onClick={() => setSelectedCollection('all')}
              className={`nav-item ${selectedCollection === 'all' ? 'active' : ''}`}
            >
              <BookOpen size={18} />
              {!collapsed && (
                <>
                  <span>All Bookmarks</span>
                  <span className="count">{totalBookmarks}</span>
                </>
              )}
            </button>

            <button
              onClick={() => setSelectedCollection('favorites')}
              className={`nav-item ${selectedCollection === 'favorites' ? 'active' : ''}`}
            >
              <Star size={18} />
              {!collapsed && (
                <>
                  <span>Favorites</span>
                  <span className="count">{favoriteBookmarks}</span>
                </>
              )}
            </button>

            <button
              onClick={() => setSelectedCollection('recent')}
              className={`nav-item ${selectedCollection === 'recent' ? 'active' : ''}`}
            >
              <Clock size={18} />
              {!collapsed && (
                <>
                  <span>Recently Added</span>
                  <span className="count">{recentBookmarks}</span>
                </>
              )}
            </button>
          </nav>
        </div>

        {/* Collections Section */}
        {!collapsed && (
          <div className="sidebar-section">
            <div className="section-header">
              <button
                onClick={() => setCollectionsExpanded(!collectionsExpanded)}
                className="section-toggle"
              >
                {collectionsExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                <h3 className="section-title">Collections</h3>
              </button>
              <button className="add-btn" aria-label="Add collection">
                <Plus size={14} />
              </button>
            </div>

            {collectionsExpanded && (
              <nav className="nav-list">
                {collections.map(collection => (
                  <button
                    key={collection.id}
                    onClick={() => setSelectedCollection(collection.name)}
                    className={`nav-item ${selectedCollection === collection.name ? 'active' : ''}`}
                  >
                    <Folder size={18} />
                    <span>{collection.name}</span>
                    <span
                      className="collection-indicator"
                      style={{ backgroundColor: collection.color }}
                    />
                    <span className="count">{collection.count}</span>
                  </button>
                ))}
              </nav>
            )}
          </div>
        )}

        {/* Tags Section */}
        {!collapsed && (
          <div className="sidebar-section">
            <div className="section-header">
              <button
                onClick={() => setTagsExpanded(!tagsExpanded)}
                className="section-toggle"
              >
                {tagsExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                <h3 className="section-title">Tags</h3>
              </button>
            </div>

            {tagsExpanded && (
              <div className="tags-list">
                {tags.slice(0, 15).map(tag => {
                  const count = getTagCount(tag)
                  const isSelected = selectedTags.includes(tag)

                  return (
                    <button
                      key={tag}
                      onClick={() => handleTagToggle(tag)}
                      className={`tag-item ${isSelected ? 'selected' : ''}`}
                    >
                      <Tag size={14} />
                      <span className="tag-name">{tag}</span>
                      <span className="tag-count">{count}</span>
                    </button>
                  )
                })}

                {tags.length > 15 && (
                  <button className="show-more-tags">
                    Show {tags.length - 15} more...
                  </button>
                )}
              </div>
            )}
          </div>
        )}

        {/* Quick Actions Section */}
        {!collapsed && (
          <div className="sidebar-section">
            <div className="section-header">
              <button
                onClick={() => setQuickActionsExpanded(!quickActionsExpanded)}
                className="section-toggle"
              >
                {quickActionsExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                <h3 className="section-title">Quick Actions</h3>
              </button>
            </div>

            {quickActionsExpanded && (
              <div className="quick-actions">
                <button className="action-item">
                  <Zap size={16} />
                  <span>Auto-organize</span>
                </button>
                <button className="action-item">
                  <Plus size={16} />
                  <span>Add bookmark</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </aside>
  )
}
