/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color Scheme */
  --primary-bg: #1a1d23;
  --secondary-bg: #242832;
  --tertiary-bg: #2d3139;
  --accent-color: #4a9eff;
  --accent-hover: #3d8bea;
  --text-primary: #ffffff;
  --text-secondary: #b4b8c0;
  --text-muted: #8a8f98;
  --border-color: #2d3139;
  --border-hover: #3a3f4a;
  --success-color: #22c55e;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --purple-color: #8b5cf6;
  --cyan-color: #06b6d4;

  /* Spacing */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 64px;
  --header-height: 72px;
  --padding-sm: 8px;
  --padding-md: 16px;
  --padding-lg: 24px;
  --padding-xl: 32px;

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-medium: 0.25s ease-out;
  --transition-slow: 0.4s ease-out;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.5;
}

.bookmark-manager {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Sidebar Layout Styles - Visual styles handled by glass morphism in main App.css */
.sidebar {
  width: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-medium);
  position: relative;
  z-index: 100;
  /* Background and border handled by glass morphism styles */
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: var(--padding-lg);
  display: flex;
  align-items: center;
  gap: var(--padding-md);
  min-height: var(--header-height);
  /* Background and border handled by glass morphism styles */
}

.collapse-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  opacity: 1;
  transition: opacity var(--transition-medium);
}

.sidebar.collapsed .sidebar-title {
  opacity: 0;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--padding-md);
}

.sidebar-section {
  margin-bottom: var(--padding-xl);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--padding-md);
  padding: 0 var(--padding-sm);
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.library-stats {
  display: flex;
  gap: var(--padding-sm);
  font-size: 12px;
  color: var(--text-muted);
}

.section-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.section-toggle:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.add-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.add-btn:hover {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.nav-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--padding-md);
  padding: var(--padding-md);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 14px;
  text-align: left;
  width: 100%;
  position: relative;
}

.nav-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.nav-item .count {
  margin-left: auto;
  background-color: var(--tertiary-bg);
  color: var(--text-muted);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.nav-item.active .count {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

.collection-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: auto;
  margin-right: var(--padding-sm);
}

.tags-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 6px var(--padding-sm);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  font-size: 13px;
  text-align: left;
  width: 100%;
}

.tag-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.tag-item.selected {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.tag-name {
  flex: 1;
  text-align: left;
}

.tag-count {
  font-size: 11px;
  color: var(--text-muted);
  background-color: var(--tertiary-bg);
  padding: 1px 4px;
  border-radius: 8px;
}

.tag-item.selected .tag-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

.show-more-tags {
  background: none;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  padding: var(--padding-sm);
  font-size: 13px;
  text-align: left;
  transition: color var(--transition-fast);
}

.show-more-tags:hover {
  color: var(--accent-hover);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: var(--padding-md);
  padding: var(--padding-md);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 14px;
  text-align: left;
  width: 100%;
}

.action-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 0;
  transition: margin-left var(--transition-medium);
}

.main-content.sidebar-collapsed {
  margin-left: 0;
}

/* Header Styles */
.header {
  height: var(--header-height);
  background-color: var(--secondary-bg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 var(--padding-xl);
  gap: var(--padding-xl);
  position: relative;
  z-index: 50;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--padding-md);
}

.header-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.bookmark-count {
  color: var(--text-muted);
  font-size: 14px;
}

.header-center {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--padding-lg);
  justify-content: center;
  max-width: 600px;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  background-color: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: 14px;
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
}

.search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 20px;
  padding: 2px;
  border-radius: 50%;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.filter-buttons {
  display: flex;
  gap: 4px;
  background-color: var(--tertiary-bg);
  padding: 4px;
  border-radius: var(--radius-lg);
}

.filter-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 13px;
  font-weight: 500;
}

.filter-btn:hover {
  color: var(--text-primary);
}

.filter-btn.active {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--padding-md);
}

.import-btn {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 10px 16px;
  background-color: var(--accent-color);
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
}

.import-btn:hover {
  background-color: var(--accent-hover);
}

.import-btn.active {
  background-color: var(--accent-hover);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.action-btn:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

/* Content Wrapper */
.content-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Bookmark Grid Styles */
.bookmark-grid-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--padding-xl);
}

.grid-header {
  margin-bottom: var(--padding-xl);
}

.grid-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--padding-sm);
}

.grid-subtitle {
  color: var(--text-secondary);
  font-size: 16px;
}

.bookmark-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--padding-lg);
  padding-bottom: var(--padding-xl);
}

/* Bookmark Card Styles */
.bookmark-card {
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--padding-lg);
  cursor: pointer;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.bookmark-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
}

.bookmark-card.dragging {
  opacity: 0.6;
  transform: rotate(5deg);
}

.bookmark-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--padding-md);
}

.bookmark-favicon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--tertiary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.favicon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.favicon-fallback {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.bookmark-actions {
  display: flex;
  gap: var(--padding-sm);
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.bookmark-card:hover .bookmark-actions {
  opacity: 1;
}

.favorite-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--warning-color);
}

.favorite-btn.active {
  color: var(--warning-color);
}

.menu-container {
  position: relative;
}

.menu-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--padding-sm);
  min-width: 150px;
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-sm) var(--padding-md);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 14px;
  text-align: left;
  width: 100%;
}

.menu-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.menu-item.danger {
  color: var(--error-color);
}

.menu-item.danger:hover {
  background-color: var(--error-color);
  color: var(--text-primary);
}

.menu-separator {
  margin: var(--padding-sm) 0;
  border: none;
  border-top: 1px solid var(--border-color);
}

.bookmark-content {
  margin-bottom: var(--padding-lg);
}

.bookmark-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--padding-sm);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bookmark-description {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: var(--padding-md);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bookmark-url {
  color: var(--text-muted);
  font-size: 13px;
  font-weight: 500;
}

.bookmark-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  gap: var(--padding-md);
}

.bookmark-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  flex: 1;
}

.tag {
  background-color: var(--tertiary-bg);
  color: var(--text-muted);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.tag-more {
  background-color: var(--tertiary-bg);
  color: var(--text-muted);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.bookmark-meta {
  display: flex;
  gap: var(--padding-md);
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-muted);
  font-size: 12px;
}

.bookmark-collection-indicator {
  position: absolute;
  top: var(--padding-md);
  right: var(--padding-md);
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.bookmark-card:hover .bookmark-collection-indicator {
  opacity: 1;
}

.collection-badge {
  background-color: var(--accent-color);
  color: var(--text-primary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 500;
}

/* Skeleton Styles */
.bookmark-card.skeleton {
  pointer-events: none;
}

.skeleton-avatar {
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-btn {
  width: 32px;
  height: 32px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

.skeleton-title {
  height: 20px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
  margin-bottom: var(--padding-sm);
}

.skeleton-description {
  margin-bottom: var(--padding-md);
}

.skeleton-line {
  height: 14px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
  margin-bottom: 4px;
}

.skeleton-line.short {
  width: 70%;
}

.skeleton-url {
  height: 12px;
  width: 60%;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
  margin-bottom: var(--padding-lg);
}

.skeleton-tag {
  height: 16px;
  width: 40px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 10px;
}

.skeleton-meta {
  height: 12px;
  width: 30px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* Empty State Styles */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--padding-xl) * 2;
  height: 100%;
}

.empty-icon {
  color: var(--text-muted);
  margin-bottom: var(--padding-lg);
  opacity: 0.6;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--padding-md);
}

.empty-description {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: var(--padding-xl);
  max-width: 400px;
}

.empty-actions {
  display: flex;
  gap: var(--padding-md);
  flex-wrap: wrap;
  justify-content: center;
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 12px 24px;
  background-color: var(--accent-color);
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
}

.btn-primary:hover {
  background-color: var(--accent-hover);
}

.btn-secondary {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 12px 24px;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
}

.btn-secondary:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

/* Import Panel Styles */
.import-panel {
  width: 360px;
  background-color: var(--secondary-bg);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.import-header {
  padding: var(--padding-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.import-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.import-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--padding-lg);
}

.import-section {
  margin-bottom: var(--padding-xl);
}

.import-section .section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--padding-md);
  text-transform: none;
  letter-spacing: normal;
}

.section-description {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: var(--padding-md);
  line-height: 1.4;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

.format-option {
  display: flex;
  align-items: center;
  gap: var(--padding-md);
  padding: var(--padding-md);
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  text-align: left;
  width: 100%;
}

.format-option:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.format-option.active {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--text-primary);
}

.format-option span {
  font-weight: 500;
}

.format-option small {
  display: block;
  opacity: 0.8;
  font-size: 12px;
  margin-top: 2px;
}

.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--padding-xl);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-medium);
  background-color: var(--tertiary-bg);
  position: relative;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: var(--accent-color);
  background-color: rgba(74, 158, 255, 0.05);
}

.upload-area.dragging {
  border-color: var(--accent-color);
  background-color: rgba(74, 158, 255, 0.1);
  transform: scale(1.02);
}

.upload-area.processing {
  cursor: default;
  pointer-events: none;
}

.file-input {
  display: none;
}

.upload-text {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: var(--padding-sm);
}

.upload-hint {
  color: var(--text-muted);
  font-size: 14px;
}

.processing-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--tertiary-bg);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--padding-md);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--tertiary-bg);
  border-radius: 2px;
  overflow: hidden;
  margin-top: var(--padding-md);
}

.progress-fill {
  height: 100%;
  background-color: var(--accent-color);
  transition: width var(--transition-medium);
}

.success-icon {
  color: var(--success-color);
  margin-bottom: var(--padding-md);
}

.error-icon {
  color: var(--error-color);
  margin-bottom: var(--padding-md);
}

.template-actions {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

.template-btn {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  text-align: left;
  width: 100%;
}

.template-btn:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.browser-instructions {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: var(--padding-md);
  padding: var(--padding-md);
  background-color: var(--tertiary-bg);
  border-radius: var(--radius-lg);
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .bookmark-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .header-center {
    max-width: 400px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform var(--transition-medium);
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .header {
    padding: 0 var(--padding-lg);
  }

  .header-center {
    flex-direction: column;
    gap: var(--padding-md);
    align-items: stretch;
  }

  .bookmark-grid {
    grid-template-columns: 1fr;
    gap: var(--padding-md);
  }

  .bookmark-grid-container {
    padding: var(--padding-lg);
  }

  .import-panel {
    width: 100%;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform var(--transition-medium);
  }

  .import-panel.open {
    transform: translateX(0);
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    height: auto;
    padding: var(--padding-md);
    gap: var(--padding-md);
  }

  .header-left {
    align-self: flex-start;
  }

  .header-right {
    align-self: flex-end;
  }

  .bookmark-card {
    padding: var(--padding-md);
  }

  .empty-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Focus and Accessibility */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* Selection */
::selection {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

/* Print Styles */
@media print {

  .sidebar,
  .header,
  .import-panel {
    display: none;
  }

  .main-content {
    margin: 0;
  }

  .bookmark-card {
    break-inside: avoid;
    margin-bottom: var(--padding-md);
  }
}