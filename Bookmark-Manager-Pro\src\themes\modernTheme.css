/* Modern Theme - Cohesive Design System */
/* Extends the beautiful panel styling throughout the entire application */
/* Integrates with existing theme system and CSS variables */

.theme-modern {
  /* Modern Design Tokens - Override existing CSS variables */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
  --radius-sm: 6px !important;
  --radius-md: 8px !important;
  --radius-lg: 12px !important;
  --radius-xl: 16px !important;
  --transition-fast: 0.15s ease !important;
  --transition-medium: 0.3s ease !important;
  --transition-slow: 0.5s ease !important;

  /* Modern Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

  /* Glass Morphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-blur: blur(20px);

  /* Enhanced Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
}

/* Modern Theme Integration with Existing Theme System */
/* Uses existing CSS variables but enhances them with modern styling */

.theme-modern {
  /* Enhance existing theme colors with modern effects */
  --modern-primary-bg: var(--primary-bg);
  --modern-secondary-bg: var(--secondary-bg);
  --modern-tertiary-bg: var(--tertiary-bg);
  --modern-accent-color: var(--accent-color);
  --modern-accent-hover: var(--accent-hover);
  --modern-text-primary: var(--text-primary);
  --modern-text-secondary: var(--text-secondary);
  --modern-text-muted: var(--text-muted);
  --modern-border-color: var(--border-color);
  --modern-border-hover: var(--border-hover);
  --modern-success-color: var(--success-color);
  --modern-warning-color: var(--warning-color);
  --modern-error-color: var(--error-color);

  /* Modern enhancements */
  --modern-glass-bg: rgba(var(--primary-bg-rgb, 255, 255, 255), 0.95);
  --modern-glass-secondary: rgba(var(--secondary-bg-rgb, 248, 250, 252), 0.95);
  --modern-backdrop-blur: blur(20px);
}

/* Global Modern Styling */
.theme-modern {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: var(--primary-bg);
  color: var(--text-primary);
  transition: all var(--transition-medium);
}

/* Modern Header Styling - Adapts to Current Theme */
.theme-modern .header,
.theme-modern .app-header,
.theme-modern header {
  background: var(--modern-glass-bg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
  border-bottom: 2px solid var(--modern-border-color) !important;
  box-shadow: var(--shadow-lg) !important;
  padding: var(--spacing-md) var(--spacing-lg) !important;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg) !important;
  transition: all var(--transition-medium) !important;
  position: relative;
  z-index: 100;
}

.theme-modern .header::before,
.theme-modern .app-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(var(--accent-color-rgb, 59, 130, 246), 0.05) 0%,
      rgba(var(--accent-color-rgb, 59, 130, 246), 0.02) 100%);
  pointer-events: none;
  border-radius: inherit;
}

.theme-modern .header-title {
  font-size: 24px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Sidebar Styling - Deferred to main App.css glass morphism styles */
.theme-modern .sidebar,
.theme-modern .app-sidebar {
  /* Glass morphism styles handled by main App.css for consistency */
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0 !important;
  transition: all var(--transition-medium) !important;
  position: relative;
  overflow: hidden;
}

.theme-modern .sidebar::before,
.theme-modern .app-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg,
      rgba(var(--accent-color-rgb, 59, 130, 246), 0.03) 0%,
      transparent 50%,
      rgba(var(--accent-color-rgb, 59, 130, 246), 0.01) 100%);
  pointer-events: none;
}

.theme-modern .sidebar-item {
  padding: 12px 16px;
  margin: 4px 8px;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.theme-modern .sidebar-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.theme-modern .sidebar-item.active {
  background: rgba(59, 130, 246, 0.15);
  border-color: var(--accent-color);
  box-shadow: var(--shadow-lg);
}

.theme-modern .sidebar-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--accent-color);
  transform: scaleY(0);
  transition: transform var(--transition-fast);
}

.theme-modern .sidebar-item.active::before {
  transform: scaleY(1);
}

/* Modern Button Styling */
.theme-modern .btn {
  padding: 10px 20px;
  border-radius: var(--radius-md);
  border: 2px solid transparent;
  font-weight: 500;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.theme-modern .btn-primary {
  background: var(--accent-color);
  color: white;
  box-shadow: var(--shadow-md);
}

.theme-modern .btn-primary:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.theme-modern .btn-secondary {
  background: var(--secondary-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.theme-modern .btn-secondary:hover {
  border-color: var(--accent-color);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
}

/* Modern Input Styling */
.theme-modern .input {
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--primary-bg);
  transition: all var(--transition-fast);
  font-size: 14px;
}

.theme-modern .input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

/* Modern Search Bar */
.theme-modern .search-container {
  position: relative;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 4px;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.theme-modern .search-input {
  border: none;
  background: transparent;
  padding: 12px 20px;
  border-radius: var(--radius-lg);
  width: 100%;
}

/* Modern Panel Styling (Right Panels) */
.theme-modern .right-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: 24px;
  margin: 16px;
}

.theme-modern .panel-header {
  padding-bottom: 16px;
  border-bottom: 2px solid var(--border-color);
  margin-bottom: 20px;
}

.theme-modern .panel-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* Modern Tab Styling */
.theme-modern .tab-container {
  background: var(--secondary-bg);
  border-radius: var(--radius-lg);
  padding: 4px;
  margin-bottom: 20px;
}

.theme-modern .tab {
  padding: 10px 16px;
  border-radius: var(--radius-md);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.theme-modern .tab.active {
  background: var(--primary-bg);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
}

.theme-modern .tab:hover:not(.active) {
  background: rgba(59, 130, 246, 0.1);
  color: var(--accent-color);
}

/* Modern Card Styling (extends bookmark cards) */
.theme-modern .card {
  background: var(--primary-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 20px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.theme-modern .card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--accent-color);
}

/* Modern List Styling */
.theme-modern .list-item {
  padding: 12px 16px;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  transition: all var(--transition-fast);
  margin-bottom: 4px;
}

.theme-modern .list-item:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateX(4px);
}

/* Modern Badge/Tag Styling */
.theme-modern .badge {
  padding: 4px 12px;
  border-radius: var(--radius-xl);
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  box-shadow: var(--shadow-sm);
}

/* Modern Tooltip Styling */
.theme-modern .tooltip {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  font-size: 12px;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
}

/* Modern Scrollbar */
.theme-modern ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.theme-modern ::-webkit-scrollbar-track {
  background: var(--secondary-bg);
  border-radius: var(--radius-sm);
}

.theme-modern ::-webkit-scrollbar-thumb {
  background: var(--border-hover);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

.theme-modern ::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color);
}

/* Modern Animation Classes */
.theme-modern .fade-in {
  animation: modernFadeIn 0.3s ease-out;
}

.theme-modern .slide-up {
  animation: modernSlideUp 0.3s ease-out;
}

.theme-modern .scale-in {
  animation: modernScaleIn 0.2s ease-out;
}

@keyframes modernFadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes modernSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modernScaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced Application Layout Styling */
.theme-modern .app-container {
  background: var(--secondary-bg) !important;
}

.theme-modern .main-content {
  background: var(--primary-bg) !important;
  border-radius: var(--radius-lg);
  margin: 16px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

/* Modern Search Bar Enhancement */
.theme-modern .search-bar,
.theme-modern .search-container {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 2px solid var(--border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.theme-modern .search-bar:focus-within,
.theme-modern .search-container:focus-within {
  border-color: var(--accent-color) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-lg) !important;
}

/* Modern Grid Container */
.theme-modern .bookmark-grid-container {
  background: var(--primary-bg) !important;
  border-radius: var(--radius-lg);
  padding: 20px;
  margin: 16px;
  box-shadow: var(--shadow-md);
}

.theme-modern .grid-header {
  border-bottom: 2px solid var(--border-color) !important;
  padding-bottom: 16px;
  margin-bottom: 20px;
}

.theme-modern .grid-title {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Modern Filter and View Controls */
.theme-modern .filter-controls,
.theme-modern .view-controls {
  background: var(--secondary-bg) !important;
  border-radius: var(--radius-lg);
  padding: 12px;
  border: 2px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

/* Modern Collection Items in Sidebar - Theme Adaptive */
.theme-modern .collection-item,
.theme-modern .sidebar-collection,
.theme-modern .sidebar-item,
.theme-modern .nav-item {
  padding: var(--spacing-sm) var(--spacing-md) !important;
  margin: var(--spacing-xs) var(--spacing-sm) !important;
  border-radius: var(--radius-md) !important;
  transition: all var(--transition-fast) !important;
  border: 2px solid transparent !important;
  position: relative;
  overflow: hidden;
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.02) !important;
}

.theme-modern .collection-item:hover,
.theme-modern .sidebar-collection:hover,
.theme-modern .sidebar-item:hover,
.theme-modern .nav-item:hover {
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.1) !important;
  border-color: rgba(var(--accent-color-rgb, 59, 130, 246), 0.3) !important;
  transform: translateX(4px) !important;
  box-shadow: var(--shadow-md) !important;
}

.theme-modern .collection-item.active,
.theme-modern .sidebar-collection.active,
.theme-modern .sidebar-item.active,
.theme-modern .nav-item.active {
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.15) !important;
  border-color: var(--modern-accent-color) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* Active indicator line */
.theme-modern .collection-item.active::before,
.theme-modern .sidebar-collection.active::before,
.theme-modern .sidebar-item.active::before,
.theme-modern .nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--modern-accent-color);
  border-radius: 0 2px 2px 0;
}

/* Modern Right Panel Enhancements - Theme Adaptive */
.theme-modern .right-panel-container,
.theme-modern .right-panel,
.theme-modern .panel,
.theme-modern .tabbed-panel,
.theme-modern .export-panel,
.theme-modern .import-panel,
.theme-modern .auto-organize-panel,
.theme-modern .split-panel,
.theme-modern .playlist-panel {
  background: var(--modern-glass-bg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  margin: var(--spacing-md) !important;
  padding: var(--spacing-lg) !important;
  position: relative;
  overflow: hidden;
}

/* Panel gradient overlay */
.theme-modern .right-panel-container::before,
.theme-modern .right-panel::before,
.theme-modern .panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(135deg,
      rgba(var(--accent-color-rgb, 59, 130, 246), 0.05) 0%,
      transparent 100%);
  pointer-events: none;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

/* Modern Modal and Dialog Styling */
.theme-modern .modal,
.theme-modern .dialog {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  border: 2px solid var(--border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
}

/* Modern Smart Playlist Panel Styling */
.theme-modern .playlist-panel {
  background: var(--modern-glass-bg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
}

.theme-modern .playlist-header {
  background: linear-gradient(135deg,
      rgba(var(--accent-color-rgb, 59, 130, 246), 0.05) 0%,
      transparent 100%) !important;
  border-bottom: 2px solid var(--modern-border-color) !important;
  padding: var(--spacing-lg) !important;
}

.theme-modern .playlist-title {
  color: var(--modern-text-primary) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.theme-modern .smart-controls {
  display: flex;
  gap: var(--spacing-sm);
  margin-left: auto;
}

.theme-modern .smart-btn {
  padding: var(--spacing-sm) var(--spacing-md) !important;
  border: 2px solid transparent !important;
  border-radius: var(--radius-md) !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  cursor: pointer;
  transition: all var(--transition-fast) !important;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  backdrop-filter: blur(10px) !important;
  box-shadow: var(--shadow-md) !important;
  position: relative;
  overflow: hidden;
}

.theme-modern .smart-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.theme-modern .smart-btn:hover::before {
  left: 100%;
}

.theme-modern .generate-btn {
  background: linear-gradient(135deg, var(--gradient-primary)) !important;
  color: white !important;
  border-color: rgba(var(--accent-color-rgb, 59, 130, 246), 0.3) !important;
}

.theme-modern .generate-btn:hover:not(:disabled) {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-xl) !important;
  border-color: var(--modern-accent-color) !important;
}

.theme-modern .auto-create-btn {
  background: linear-gradient(135deg, var(--gradient-success)) !important;
  color: white !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
}

.theme-modern .auto-create-btn:hover:not(:disabled) {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-xl) !important;
  border-color: var(--modern-success-color) !important;
}

/* Modern Smart Suggestions Panel */
.theme-modern .smart-suggestions-panel {
  background: var(--modern-glass-secondary) !important;
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-lg) !important;
  margin-bottom: var(--spacing-md) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
  box-shadow: var(--shadow-lg) !important;
}

.theme-modern .suggestions-header {
  background: linear-gradient(135deg,
      rgba(var(--accent-color-rgb, 59, 130, 246), 0.1) 0%,
      transparent 100%) !important;
  border-bottom: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--spacing-md) !important;
  margin-bottom: var(--spacing-md) !important;
}

.theme-modern .suggestions-header h4 {
  color: var(--modern-text-primary) !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  margin: 0 !important;
}

.theme-modern .close-suggestions-btn {
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.1) !important;
  border: 1px solid var(--modern-border-color) !important;
  color: var(--modern-text-secondary) !important;
  border-radius: var(--radius-sm) !important;
  padding: var(--spacing-xs) !important;
  transition: all var(--transition-fast) !important;
}

.theme-modern .close-suggestions-btn:hover {
  background: var(--modern-accent-color) !important;
  color: white !important;
  transform: scale(1.1) !important;
}

/* Modern Analytics Modal */
.theme-modern .analytics-modal {
  background: var(--modern-glass-bg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
}

.theme-modern .analytics-header {
  background: linear-gradient(135deg,
      rgba(var(--accent-color-rgb, 59, 130, 246), 0.1) 0%,
      transparent 100%) !important;
  border-bottom: 2px solid var(--modern-border-color) !important;
  padding: var(--spacing-lg) !important;
}

.theme-modern .analytics-content {
  padding: var(--spacing-lg) !important;
}

.theme-modern .analytics-section {
  background: var(--modern-glass-secondary) !important;
  border: 1px solid var(--modern-border-color) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-md) !important;
  margin-bottom: var(--spacing-md) !important;
  backdrop-filter: blur(5px) !important;
}

.theme-modern .analytics-section h4 {
  color: var(--modern-text-primary) !important;
  font-weight: 600 !important;
  margin-bottom: var(--spacing-sm) !important;
}

/* Modern Playlist Items */
.theme-modern .playlist-item {
  background: var(--modern-glass-secondary) !important;
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-lg) !important;
  margin-bottom: var(--spacing-sm) !important;
  transition: all var(--transition-fast) !important;
  backdrop-filter: blur(5px) !important;
  box-shadow: var(--shadow-sm) !important;
}

.theme-modern .playlist-item:hover {
  border-color: var(--modern-accent-color) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.theme-modern .playlist-info {
  padding: var(--spacing-md) !important;
}

.theme-modern .playlist-name {
  color: var(--modern-text-primary) !important;
  font-weight: 600 !important;
}

.theme-modern .playlist-description {
  color: var(--modern-text-secondary) !important;
}

.theme-modern .playlist-meta {
  color: var(--modern-text-muted) !important;
}

.theme-modern .auto-create-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  color: white !important;
}

.theme-modern .auto-create-btn:hover:not(:disabled) {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-xl) !important;
}

.theme-modern .smart-btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Smart Suggestions Panel */
.theme-modern .smart-suggestions-panel {
  background: var(--modern-glass-bg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-lg) !important;
  margin-bottom: var(--spacing-lg) !important;
  padding: var(--spacing-md) !important;
  box-shadow: var(--shadow-lg) !important;
}

.theme-modern .suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md) !important;
  padding-bottom: var(--spacing-sm) !important;
  border-bottom: 1px solid var(--modern-border-color) !important;
}

.theme-modern .suggestion-card {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid var(--modern-border-color) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--spacing-md) !important;
  margin-bottom: var(--spacing-sm) !important;
  transition: all var(--transition-fast) !important;
}

.theme-modern .suggestion-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.theme-modern .suggestion-header {
  display: flex;
  gap: var(--spacing-sm) !important;
  margin-bottom: var(--spacing-sm) !important;
}

.theme-modern .suggestion-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.theme-modern .suggestion-meta {
  display: flex;
  gap: var(--spacing-md) !important;
  font-size: 12px;
  color: var(--text-muted) !important;
  margin-top: var(--spacing-xs) !important;
}

.theme-modern .create-from-suggestion-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
  color: white !important;
  border: none !important;
  padding: 6px 12px !important;
  border-radius: var(--radius-sm) !important;
  font-size: 12px !important;
  cursor: pointer;
  transition: all 0.2s ease !important;
}

.theme-modern .create-from-suggestion-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
}

/* Analytics Modal */
.theme-modern .analytics-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(5px) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.theme-modern .analytics-modal {
  background: var(--modern-glass-bg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.theme-modern .analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) !important;
  border-bottom: 1px solid var(--modern-border-color) !important;
}

.theme-modern .analytics-content {
  padding: var(--spacing-lg) !important;
  overflow-y: auto;
}

.theme-modern .analytics-section {
  margin-bottom: var(--spacing-lg) !important;
}

.theme-modern .analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md) !important;
  margin-bottom: var(--spacing-md) !important;
}

.theme-modern .analytics-card {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid var(--modern-border-color) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--spacing-md) !important;
  text-align: center;
}

.theme-modern .analytics-label {
  display: block;
  font-size: 12px;
  color: var(--text-muted) !important;
  margin-bottom: var(--spacing-xs) !important;
}

.theme-modern .analytics-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary) !important;
}

.theme-modern .category-item,
.theme-modern .domain-item,
.theme-modern .pattern-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) !important;
  margin-bottom: var(--spacing-xs) !important;
  background: rgba(255, 255, 255, 0.03) !important;
  border-radius: var(--radius-sm) !important;
}

.theme-modern .category-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 3px;
  margin: 0 var(--spacing-sm) !important;
  overflow: hidden;
}

.theme-modern .category-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%) !important;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.theme-modern .recommendation-item {
  background: rgba(255, 255, 255, 0.05) !important;
  border-left: 3px solid var(--modern-accent-color) !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  margin-bottom: var(--spacing-xs) !important;
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0 !important;
}

/* Modern Button Styling - Theme Adaptive */
.theme-modern button,
.theme-modern .btn,
.theme-modern .button {
  padding: var(--spacing-sm) var(--spacing-md) !important;
  border-radius: var(--radius-md) !important;
  border: 2px solid transparent !important;
  font-weight: 500 !important;
  transition: all var(--transition-fast) !important;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  backdrop-filter: blur(4px);
}

.theme-modern .btn-primary,
.theme-modern .button-primary {
  background: var(--modern-accent-color) !important;
  color: var(--modern-primary-bg) !important;
  box-shadow: var(--shadow-md) !important;
}

.theme-modern .btn-primary:hover,
.theme-modern .button-primary:hover {
  background: var(--modern-accent-hover) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.theme-modern .btn-secondary,
.theme-modern .button-secondary {
  background: var(--modern-secondary-bg) !important;
  color: var(--modern-text-primary) !important;
  border-color: var(--modern-border-color) !important;
}

.theme-modern .btn-secondary:hover,
.theme-modern .button-secondary:hover {
  border-color: var(--modern-accent-color) !important;
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.05) !important;
  transform: translateY(-1px) !important;
}

/* Modern Form Elements - Theme Adaptive */
.theme-modern input,
.theme-modern textarea,
.theme-modern select {
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  background: var(--modern-primary-bg) !important;
  color: var(--modern-text-primary) !important;
  transition: all var(--transition-fast) !important;
  font-size: 14px !important;
}

.theme-modern input:focus,
.theme-modern textarea:focus,
.theme-modern select:focus {
  outline: none !important;
  border-color: var(--modern-accent-color) !important;
  box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1) !important;
  transform: translateY(-1px) !important;
  background: var(--modern-primary-bg) !important;
}

/* Modern Search Elements */
.theme-modern .search-input,
.theme-modern .search-bar input {
  background: var(--modern-glass-bg) !important;
  border: 2px solid var(--modern-border-color) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--spacing-sm) var(--spacing-lg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
}

/* Modern Theme Integration with Existing Themes */
/* These styles work with any color scheme from your theme list */

.theme-modern.modern-enhanced {
  /* Enhanced glass morphism effects */
  --modern-glass-opacity: 0.95;
  --modern-blur-strength: 20px;
  --modern-border-width: 2px;
  --modern-shadow-strength: 0.15;
}

/* Modern enhancements for all theme colors */
.theme-modern .modern-enhanced {
  backdrop-filter: blur(var(--modern-blur-strength)) !important;
  border-width: var(--modern-border-width) !important;
  transition: all var(--transition-medium) !important;
}

/* Modern grid and layout enhancements */
.theme-modern .bookmark-grid,
.theme-modern .grid-container {
  background: var(--modern-glass-bg) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-lg) !important;
  margin: var(--spacing-md) !important;
  box-shadow: var(--shadow-lg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
}

/* Modern modal and dialog styling */
.theme-modern .modal,
.theme-modern .dialog,
.theme-modern .popup {
  background: var(--modern-glass-bg) !important;
  backdrop-filter: var(--modern-backdrop-blur) !important;
  border: var(--modern-border-width) solid var(--modern-border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
}

/* Modern tab styling */
.theme-modern .tab-container,
.theme-modern .tabs {
  background: var(--modern-glass-secondary) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-xs) !important;
  border: 1px solid var(--modern-border-color) !important;
}

.theme-modern .tab,
.theme-modern .tab-button {
  padding: var(--spacing-sm) var(--spacing-md) !important;
  border-radius: var(--radius-md) !important;
  border: none !important;
  background: transparent !important;
  color: var(--modern-text-secondary) !important;
  font-weight: 500 !important;
  transition: all var(--transition-fast) !important;
  cursor: pointer !important;
}

.theme-modern .tab.active,
.theme-modern .tab-button.active {
  background: var(--modern-primary-bg) !important;
  color: var(--modern-text-primary) !important;
  box-shadow: var(--shadow-sm) !important;
}

.theme-modern .tab:hover:not(.active),
.theme-modern .tab-button:hover:not(.active) {
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.1) !important;
  color: var(--modern-accent-color) !important;
}

/* Modern list and item styling */
.theme-modern .list-item,
.theme-modern .menu-item {
  padding: var(--spacing-sm) var(--spacing-md) !important;
  border-radius: var(--radius-md) !important;
  border: 1px solid transparent !important;
  transition: all var(--transition-fast) !important;
  margin-bottom: var(--spacing-xs) !important;
}

.theme-modern .list-item:hover,
.theme-modern .menu-item:hover {
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.05) !important;
  border-color: rgba(var(--accent-color-rgb, 59, 130, 246), 0.2) !important;
  transform: translateX(4px) !important;
}

/* Modern badge and tag styling */
.theme-modern .badge,
.theme-modern .tag {
  padding: var(--spacing-xs) var(--spacing-sm) !important;
  border-radius: var(--radius-xl) !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border: 1px solid rgba(var(--accent-color-rgb, 59, 130, 246), 0.2) !important;
  background: rgba(var(--accent-color-rgb, 59, 130, 246), 0.1) !important;
  color: var(--modern-accent-color) !important;
  backdrop-filter: blur(4px) !important;
}

/* Modern tooltip styling */
.theme-modern .tooltip {
  background: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  border-radius: var(--radius-md) !important;
  font-size: 12px !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}